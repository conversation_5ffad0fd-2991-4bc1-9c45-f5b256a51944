---
import ContactForm from "./ContactForm.tsx";
import ContactDetails from "./ContactDetails.astro";
import { getLangFromUrl, useTranslations } from "../../i18n/utils";

// Props for the component
interface ContactInfo {
  address: {
    company: string;
    street: string;
    city: string;
  };
  phone: {
    general: string;
    concierge: string;
  };
  email: {
    general: string;
    reservations: string;
  };
  hours: {
    weekdays: string;
    saturday: string;
    sunday: string;
  };
}

interface Props {
  contactInfo?: ContactInfo;
}

const { contactInfo } = Astro.props;
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Prepare translations object for React component
const translations = {
  title: t("contact.form.title"),
  successTitle: t("contact.form.successTitle"),
  successMessage: t("contact.form.successMessage"),
  travelType: t("contact.form.travelType"),
  travelTypePlaceholder: t("contact.form.travelTypePlaceholder"),
  travelTypeOptions: {
    ski: t("contact.form.travelTypeOptions.ski"),
    luxury: t("contact.form.travelTypeOptions.luxury"),
    family: t("contact.form.travelTypeOptions.family"),
    adventure: t("contact.form.travelTypeOptions.adventure"),
    other: t("contact.form.travelTypeOptions.other"),
  },
  name: t("contact.form.name"),
  namePlaceholder: t("contact.form.namePlaceholder"),
  email: t("contact.form.email"),
  emailPlaceholder: t("contact.form.emailPlaceholder"),
  phone: t("contact.form.phone"),
  phonePlaceholder: t("contact.form.phonePlaceholder"),
  travelDates: t("contact.form.travelDates"),
  travelDatesPlaceholder: t("contact.form.travelDatesPlaceholder"),
  message: t("contact.form.message"),
  messagePlaceholder: t("contact.form.messagePlaceholder"),
  preferredContact: t("contact.form.preferredContact"),
  preferredContactEmail: t("contact.form.preferredContactEmail"),
  preferredContactPhone: t("contact.form.preferredContactPhone"),
  privacyPolicyPrefix: t("contact.form.privacyPolicyPrefix"),
  privacyPolicyLink: t("contact.form.privacyPolicyLink"),
  privacyPolicyAnd: t("contact.form.privacyPolicyAnd"),
  bookingConditionsLink: t("contact.form.bookingConditionsLink"),
  submit: t("contact.form.submit"),
};
---

<!-- Contact Form & Details -->
<section id="contact-form" class="py-12 container-custom">
  <div class="mx-auto">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-baskervville mb-3">
        {t("contact.form.sectionTitle")}
      </h2>
      <p class="text-foreground/70 max-w-2xl mx-auto text-sm">
        {t("contact.form.sectionDescription")}
      </p>
    </div>

    <!-- New unified contact card design -->
    <div
      class="bg-white rounded-lg shadow-md border border-border/10 overflow-hidden"
    >
      <div class="grid grid-cols-1 lg:grid-cols-5">
        <!-- Contact Details - Left side -->
        <div
          class="lg:col-span-2 bg-[#F8FAFC] p-8 flex flex-col justify-between relative"
          style="background-image: url('data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23285da6\' fill-opacity=\'0.03\' fill-rule=\'evenodd\'/%3E%3C/svg%3E'); background-position: center center;"
        >
          <div class="relative z-10">
            <ContactDetails contactInfo={contactInfo} />
          </div>
        </div>

        <!-- Contact Form - Right side -->
        <div class="lg:col-span-3 p-8 md:p-10">
          <ContactForm
            translationsJson={JSON.stringify(translations)}
            client:only="react"
          />
        </div>
      </div>
    </div>
  </div>
</section>
