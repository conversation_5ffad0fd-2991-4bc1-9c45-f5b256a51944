import React, { useState, useEffect } from "react";
import { getHotelsList, getHotelAndRoomTypes } from "../../utils/dataService";
import { fetchHotelsAvailability } from "../../utils/store/hotels";
import HotelDetailModal from "../hotels/HotelDetailModal";

import "../../styles/featured-hotels.css";
import "../../styles/hotel-modal.css";
import HotelCard from "../cards/HotelCard";

// Define types for our hotel data
interface HotelImage {
  id: string;
  url: string;
  metadata: Record<string, any>;
  rank: number;
  hotel_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: null | string;
}

interface AvailableRoom {
  id: string;
  title: string;
  description: string;
  thumbnail: string | null;
  metadata: {
    type: string;
    bed_type: string;
    hotel_id: string;
    amenities: string[];
    room_size: string;
    max_adults: number;
    max_infants: number;
    max_children: number;
    price_set_id: string;
    max_occupancy: number;
    max_extra_beds: number;
  };
  available_rooms: number;
  price: {
    amount: number;
    currency_code: string;
  };
}

interface Hotel {
  id: string | number;
  name: string;
  handle?: string;
  description: string;
  rating: number;
  address: string;
  location?: string;
  is_featured: boolean;
  is_active: boolean;
  destination_id: string;
  images: HotelImage[];
  lowest_price: number | null;
  highest_price: number | null;
  category_id?: string;
  tags?: string[];
  available_rooms?: AvailableRoom[];
  thumbnail?: string | null;
}

interface FeaturedHotelsWithModalProps {
  checkIn?: string;
  checkOut?: string;
  adults?: number;
  children?: number;
  infants?: number;
  currencyCode?: string;
  destinationId?: string;
  isPetsAllowed?: boolean;
}

const FeaturedHotelsWithModal: React.FC<FeaturedHotelsWithModalProps> = ({
  checkIn,
  checkOut,
  adults = 2,
  children = 0,
  infants = 0,
  currencyCode = "USD",
  destinationId,
  isPetsAllowed = false,
}) => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [roomTypes, setRoomTypes] = useState<any[]>([]);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<{
    checkIn: string | undefined;
    checkOut: string | undefined;
    adults: number;
    children: number;
    infants: number;
    currencyCode: string;
    destinationId?: string;
    isPetsAllowed?: boolean;
  }>({
    checkIn,
    checkOut,
    adults,
    children,
    infants,
    currencyCode,
    destinationId,
    isPetsAllowed,
  });

  // Get image URL for a hotel
  const getHotelImageUrl = (hotel: Hotel): string => {
    if (hotel.images && hotel.images.length > 0) {
      return hotel.images[0]?.url;
    }
    return "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80";
  };

  // Get location for a hotel
  const getHotelLocation = (hotel: Hotel): string => {
    return hotel.destination_name ||hotel.location || hotel.address || "Luxury Destination";
  };

  // Get tags for a hotel
  const getHotelTags = (hotel: Hotel): string[] => {
    if (hotel.tags && hotel.tags.length > 0) {
      return hotel.tags;
    }
    // Generate some default tags based on hotel properties
    const defaultTags: string[] = [];
    if (hotel.is_featured) defaultTags.push("Featured");
    defaultTags.push("Luxury");
    if (hotel.rating >= 4.5) defaultTags.push("Top Rated");
    return defaultTags;
  };

  // Fetch hotels when component mounts or search parameters change
  useEffect(() => {
    const fetchHotels = async () => {
      try {
        setLoading(true);

        // Check if we have search parameters for availability
        if (searchParams.checkIn && searchParams.checkOut) {
          // Fetch hotels with availability
          const availabilityData = await fetchHotelsAvailability(
            searchParams.checkIn,
            searchParams.checkOut,
            searchParams.adults,
            searchParams.children,
            searchParams.infants,
            searchParams.currencyCode,
            searchParams.destinationId,
            searchParams.isPetsAllowed
          );

          // Map API hotel data to our component's Hotel type
          const mappedHotels = availabilityData.hotels.map((hotel) => {
            // Ensure images are in the correct format
            console.log("hotel deta", hotel);
            const hotelImages: HotelImage[] = Array.isArray(hotel.images)
              ? hotel.images.map((img: any) => {
                  if (typeof img === "string") {
                    return {
                      id: "",
                      url: img,
                      metadata: {},
                      rank: 0,
                      hotel_id: hotel.id.toString(),
                      created_at: "",
                      updated_at: "",
                      deleted_at: null,
                    };
                  }
                  return img;
                })
              : [];

            return {
              id: hotel.id,
              name: hotel.name,
              handle: hotel.handle,
              description: hotel.description,
              rating: hotel.rating || 0,
              address: hotel.address || "",
              location: hotel.location,
              is_featured: hotel.is_featured || false,
              is_active: true,
              destination_id: hotel.destination_id,
              destination_name: hotel.destination_name,
              amenities: hotel.amenities,
              images: hotelImages,
              lowest_price: hotel.available_rooms?.[0]?.price?.amount || null,
              highest_price: null,
              category_id: "",
              tags: [],
              available_rooms: hotel.available_rooms,
              thumbnail: hotel.thumbnail,
            };
          });

          setHotels(mappedHotels);
        } else {
          // Fallback to regular hotel list if no search parameters
          const hotelsList = await getHotelsList();
          setHotels(hotelsList as unknown as Hotel[]);
        }
      } catch (err) {
        console.error("Error fetching hotels:", err);
        setError("Failed to load hotels. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, [searchParams]);

  // Handle hotel selection
  const handleHotelSelect = async (hotel: Hotel, event: React.MouseEvent) => {
    event.preventDefault();

    // Set the selected hotel and open modal
    setSelectedHotel(hotel);
    setModalOpen(true);

    // Fetch hotel details and room types
    try {
      setDetailsLoading(true);
      const result = await getHotelAndRoomTypes(hotel.id);
      setHotelDetails(result.hotel);

      // Filter room types to only include available rooms
      const allRoomTypes = result.roomTypes || [];
      const availableRoomTypes = allRoomTypes.filter(
        (room) => room.available !== false
      );
      setRoomTypes(availableRoomTypes);
    } catch (err) {
      console.error("Error fetching hotel details:", err);
    } finally {
      setDetailsLoading(false);
    }
  };

  // Close the modal
  const handleCloseModal = () => {
    setModalOpen(false);
  };

  return (
    <section className="featured-hotels">
      <div className="mx-auto">
        {loading && (
          <div className="text-center py-12">
            <div
              className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
              role="status"
            >
              <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                Loading...
              </span>
            </div>
            <p className="mt-4 text-gray-600">Loading available hotels...</p>
          </div>
        )}

        {error && (
          <div className="text-center py-8 bg-red-50 rounded-lg border border-red-200 p-6 max-w-2xl mx-auto">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-red-500 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <p className="text-red-600 font-medium text-lg">{error}</p>
            <p className="text-red-500 mt-2">
              Please try again later or contact support if the problem persists.
            </p>
          </div>
        )}

        {!loading && !error && (
          <>
            {hotels.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {hotels.map((hotel) => {
                  const isSelected = selectedHotel?.id === hotel.id;
                  return (
                    <div
                      key={hotel.id}
                      className={`hotel-card-wrapper ${
                        isSelected ? "selected" : ""
                      }`}
                      onClick={(e) => handleHotelSelect(hotel, e)}
                    >
                      <div className="relative group cursor-pointer">
                        <HotelCard
                          id={hotel.id}
                          name={hotel.name}
                          location={getHotelLocation(hotel)}
                          rating={hotel.rating}
                          imageUrl={getHotelImageUrl(hotel)}
                          description={hotel.description}
                          amenities={hotel.amenities}
                          isFeatured={hotel.is_featured}
                          onCardClick={(e) => e.preventDefault()}
                          onOpenInNewTab={(e) => e.stopPropagation()}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12 bg-accent/10 rounded-lg">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-accent/20">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-primary"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-baskervville mb-2">
                  No Hotels Found
                </h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  We couldn't find any hotels matching your search criteria. Try
                  adjusting your dates or other search parameters.
                </p>
              </div>
            )}
          </>
        )}
      </div>

      {/* Hotel Detail Modal */}
      {selectedHotel && (
        <HotelDetailModal
          isOpen={modalOpen}
          onClose={handleCloseModal}
          hotelDetails={hotelDetails}
          roomTypes={roomTypes}
          hotelId={selectedHotel.id.toString()}
          loading={detailsLoading}
        />
      )}
    </section>
  );
};

export default FeaturedHotelsWithModal;
