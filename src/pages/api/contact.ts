import type { APIRoute } from "astro";

// Configuration from environment variables
const LEADS_API_URL =
  import.meta.env.LEADS_API_URL || "http://localhost:3008/api/lead/create";
const TENANT_ID = import.meta.env.LEADS_TENANT_ID || "66792252c0bf1e1809f77a11";

// Validate environment configuration
function validateEnvironmentConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!LEADS_API_URL) {
    errors.push("LEADS_API_URL environment variable is not set");
  }

  if (!TENANT_ID) {
    errors.push("LEADS_TENANT_ID environment variable is not set");
  }

  // Validate URL format
  if (LEADS_API_URL) {
    try {
      new URL(LEADS_API_URL);
    } catch {
      errors.push("LEADS_API_URL is not a valid URL");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Define the structure of the contact form data
interface ContactFormData {
  "travel-type": string;
  name: string;
  email: string;
  phone?: string;
  "travel-dates"?: string;
  message: string;
  preferredContact: "email" | "phone";
  "privacy-policy": string;
}

// Validation function for email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validation function for the form data
function validateFormData(data: ContactFormData): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push("Name is required");
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push("Email is required");
  } else if (!isValidEmail(data.email)) {
    errors.push("Please provide a valid email address");
  }

  if (!data.message || data.message.trim().length === 0) {
    errors.push("Message is required");
  }

  if (!data["travel-type"] || data["travel-type"].trim().length === 0) {
    errors.push("Travel type is required");
  }

  if (!data["privacy-policy"]) {
    errors.push("You must agree to the privacy policy and booking conditions");
  }

  // Validate preferred contact method
  if (
    data.preferredContact === "phone" &&
    (!data.phone || data.phone.trim().length === 0)
  ) {
    errors.push(
      "Phone number is required when phone is selected as preferred contact method"
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Function to process the contact form submission
async function processContactForm(
  formData: ContactFormData
): Promise<{ success: boolean; message?: string }> {
  try {
    // Parse the name into first and last name
    const nameParts = formData.name.trim().split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    // Prepare the payload for the external Leads API
    const leadPayload = {
      tenantId: TENANT_ID,
      firstName: firstName,
      lastName: lastName,
      email: formData.email,
      company: "", // Not collected in our form, so leaving empty
      phone: formData.phone || "",
      description: `Travel Type: ${formData["travel-type"]}${
        formData["travel-dates"]
          ? `\nTravel Dates: ${formData["travel-dates"]}`
          : ""
      }\nPreferred Contact: ${formData.preferredContact}\n\nMessage: ${
        formData.message
      }`,
    };

    // Submit to external Leads API
    try {
      const response = await fetch(LEADS_API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(leadPayload),
      });

      if (!response.ok) {
        throw new Error(`Leads API request failed: ${response.status}`);
      }

      await response.json();
    } catch (apiError) {
      // Continue execution - don't fail the form submission if the external API is down
    }

    return {
      success: true,
      message:
        "Your message has been submitted successfully. We will get back to you soon!",
    };
  } catch (error) {
    return {
      success: false,
      message:
        "There was an error processing your submission. Please try again later.",
    };
  }
}

// API Route handler function (shared between PUT and POST)
async function handleContactFormSubmission({ request }: { request: Request }) {
  try {
    // Validate environment configuration first
    const envValidation = validateEnvironmentConfig();
    if (!envValidation.isValid) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Server configuration error. Please contact support.",
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }

    // Parse the form data
    const formData = await request.formData();

    // Convert FormData to our ContactFormData interface
    const contactData: ContactFormData = {
      "travel-type": (formData.get("travel-type") as string) || "",
      name: (formData.get("name") as string) || "",
      email: (formData.get("email") as string) || "",
      phone: (formData.get("phone") as string) || "",
      "travel-dates": (formData.get("travel-dates") as string) || "",
      message: (formData.get("message") as string) || "",
      preferredContact:
        (formData.get("preferredContact") as "email" | "phone") || "email",
      "privacy-policy": (formData.get("privacy-policy") as string) || "",
    };

    // Validate the form data
    const validation = validateFormData(contactData);

    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          success: false,
          message: validation.errors.join(", "),
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }

    // Process the form submission
    const result = await processContactForm(contactData);

    // Return the response
    return new Response(JSON.stringify(result), {
      status: result.success ? 200 : 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        message: "Internal server error. Please try again later.",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
}

// API Route handlers
export const PUT: APIRoute = handleContactFormSubmission;
export const POST: APIRoute = handleContactFormSubmission;

// Handle GET requests (not allowed for this endpoint)
export const GET: APIRoute = async () => {
  return new Response(
    JSON.stringify({
      success: false,
      message:
        "Method not allowed. This endpoint only accepts PUT or POST requests.",
    }),
    {
      status: 405,
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
};
